@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
:root {
  --background: 251 252 250;
  --foreground: 0 0 0;
  --card: 255 255 255;
  --card-foreground: 1 1 0;
  --popover: 249 253 244;
  --popover-foreground: 1 1 0;
  --primary: 0 30 127;
  --primary-foreground: 240 243 255;
  --secondary: 49 56 69;
  --secondary-foreground: 195 201 213;
  --muted: 222 226 216;
  --muted-foreground: 117 131 97;
  --accent: 255 40 84;
  --accent-foreground: 255 255 255;
  --destructive: 255 56 43;
  --destructive-foreground: 255 255 255;
  --border: 240 244 235;
  --input: 218 228 205;
  --ring: 240 244 235;

  --android-background: 254 255 252;
  --android-foreground: 1 1 1;
  --android-card: 255 255 255;
  --android-card-foreground: 1 1 0;
  --android-popover: 249 253 244;
  --android-popover-foreground: 1 1 0;
  --android-primary: 0 30 127;
  --android-primary-foreground: 240 243 255;
  --android-secondary: 192 214 255;
  --android-secondary-foreground: 0 36 102;
  --android-muted: 0 0 0;
  --android-muted-foreground: 179 179 179;
  --android-accent: 92 43 109;
  --android-accent-foreground: 215 182 226;
  --android-destructive: 186 26 26;
  --android-destructive-foreground: 255 255 255;
  --android-border: 186 189 182;
  --android-input: 202 206 196;
  --android-ring: 186 189 182;

  --web-background: 251 254 246;
  --web-foreground: 31 33 28;
  --web-card: 255 255 255;
  --web-card-foreground: 24 28 35;
  --web-popover: 215 217 228;
  --web-popover-foreground: 0 0 0;
  --web-primary: 15 25 0;
  --web-primary-foreground: 246 255 232;
  --web-secondary: 176 201 255;
  --web-secondary-foreground: 28 60 114;
  --web-muted: 240 240 240;
  --web-muted-foreground: 122 122 122;
  --web-accent: 169 73 204;
  --web-accent-foreground: 255 255 255;
  --web-destructive: 186 26 26;
  --web-destructive-foreground: 255 255 255;
  --web-border: 235 237 233;
  --web-input: 243 244 241;
  --web-ring: 226 229 220;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: 0 0 0;
    --foreground: 255 255 254;
    --card: 36 48 20;
    --card-foreground: 255 255 254;
    --popover: 48 52 41;
    --popover-foreground: 255 255 254;
    --primary: 150 255 3;
    --primary-foreground: 0 0 0;
    --secondary: 49 56 69;
    --secondary-foreground: 195 201 213;
    --muted: 105 115 91;
    --muted-foreground: 206 211 199;
    --accent: 255 52 95;
    --accent-foreground: 255 255 255;
    --destructive: 254 67 54;
    --destructive-foreground: 255 255 255;
    --border: 69 75 59;
    --input: 82 90 70;
    --ring: 69 75 59;

    --android-background: 23 26 19;
    --android-foreground: 233 237 227;
    --android-card: 74 77 70;
    --android-card-foreground: 202 205 197;
    --android-popover: 74 77 70;
    --android-popover-foreground: 202 205 197;
    --android-primary: 150 255 3;
    --android-primary-foreground: 0 0 0;
    --android-secondary: 192 214 255;
    --android-secondary-foreground: 0 36 102;
    --android-muted: 0 0 0;
    --android-muted-foreground: 179 179 179;
    --android-accent: 92 43 109;
    --android-accent-foreground: 215 182 226;
    --android-destructive: 147 0 10;
    --android-destructive-foreground: 255 255 255;
    --android-border: 149 153 142;
    --android-input: 74 78 69;
    --android-ring: 149 153 142;

    --web-background: 21 25 17;
    --web-foreground: 230 236 223;
    --web-card: 70 74 78;
    --web-card-foreground: 197 201 206;
    --web-popover: 70 74 78;
    --web-popover-foreground: 197 201 206;
    --web-primary: 150 255 3;
    --web-primary-foreground: 13 23 0;
    --web-secondary: 28 60 114;
    --web-secondary-foreground: 255 255 255;
    --web-muted: 38 38 38;
    --web-muted-foreground: 179 179 179;
    --web-accent: 83 0 111;
    --web-accent-foreground: 255 255 255;
    --web-destructive: 147 0 10;
    --web-destructive-foreground: 255 255 255;
    --web-border: 62 67 55;
    --web-input: 52 58 43;
    --web-ring: 78 87 65;
  }
}
}