import React from 'react';
import { View, Text } from 'react-native';
import { cn } from '@/lib/utils';

interface StatCardProps {
  value: string;
  label: string;
  className?: string;
}

export function StatCard({ value, label, className }: StatCardProps) {
  return (
    <View className={cn("text-center p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl", className)}>
      <Text className="text-3xl font-bold text-gray-900 dark:text-white">{value}</Text>
      <Text className="text-sm text-gray-500 dark:text-gray-400 font-medium">{label}</Text>
    </View>
  );
}
