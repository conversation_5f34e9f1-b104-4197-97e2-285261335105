import React from 'react';
import { View, Pressable } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, withDelay } from 'react-native-reanimated';
import { cn } from '@/lib/utils';

interface BarData {
  height: number; // 0-100 percentage
  label?: string;
  isActive?: boolean;
}

interface BarChartProps {
  data: BarData[];
  className?: string;
  onBarPress?: (index: number) => void;
}

export function BarChart({ data, className, onBarPress }: BarChartProps) {
  return (
    <View className={cn("flex-row items-end justify-between space-x-2", className)}>
      {data.map((bar, index) => (
        <BarItem
          key={index}
          height={bar.height}
          isActive={bar.isActive}
          delay={index * 100}
          onPress={() => onBarPress?.(index)}
        />
      ))}
    </View>
  );
}

interface BarItemProps {
  height: number;
  isActive?: boolean;
  delay?: number;
  onPress?: () => void;
}

function BarItem({ height, isActive, delay = 0, onPress }: BarItemProps) {
  const animatedHeight = useSharedValue(0);

  React.useEffect(() => {
    animatedHeight.value = withDelay(delay, withTiming(height, { duration: 800 }));
  }, [height, delay]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      height: `${animatedHeight.value}%`,
    };
  });

  return (
    <Pressable onPress={onPress} className="flex-1 flex flex-col items-center space-y-2">
      <Animated.View 
        className={cn(
          "w-full rounded-t-lg transition-colors",
          isActive 
            ? "bg-primary shadow-lg" 
            : "bg-primary/30 hover:bg-primary/50"
        )}
        style={animatedStyle}
      />
    </Pressable>
  );
}
