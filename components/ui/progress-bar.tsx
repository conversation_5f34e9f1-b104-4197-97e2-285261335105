import React from 'react';
import { View } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, withDelay } from 'react-native-reanimated';
import { cn } from '@/lib/utils';

interface ProgressBarProps {
  progress: number; // 0-100
  className?: string;
  barClassName?: string;
  delay?: number;
}

export function ProgressBar({ progress, className, barClassName, delay = 0 }: ProgressBarProps) {
  const animatedProgress = useSharedValue(0);

  React.useEffect(() => {
    animatedProgress.value = withDelay(delay, withTiming(progress, { duration: 1000 }));
  }, [progress, delay]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      width: `${animatedProgress.value}%`,
    };
  });

  return (
    <View className={cn("w-full bg-gray-200/80 dark:bg-gray-700/50 rounded-full h-3", className)}>
      <Animated.View 
        className={cn("h-3 rounded-full shadow-sm", barClassName)}
        style={animatedStyle}
      />
    </View>
  );
}
