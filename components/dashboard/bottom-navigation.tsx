import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface NavItem {
  id: string;
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
  active?: boolean;
}

const navItems: NavItem[] = [
  { id: 'home', label: 'Home', icon: 'home', active: true },
  { id: 'focus', label: 'Focus', icon: 'time' },
  { id: 'track', label: 'Track', icon: 'bar-chart' },
  { id: 'achieve', label: 'Achieve', icon: 'trophy' },
  { id: 'tasks', label: 'Tasks', icon: 'list' },
];

export function BottomNavigation() {
  const insets = useSafeAreaInsets();

  return (
    <View 
      className="sticky bottom-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-800/50 shadow-lg"
      style={{ paddingBottom: insets.bottom }}
    >
      <View className="flex-row justify-around items-center px-4 py-3">
        {navItems.map((item) => (
          <Pressable
            key={item.id}
            className={`flex-col items-center gap-1.5 p-2 rounded-xl ${
              item.active 
                ? 'bg-primary/10 text-primary' 
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50'
            } transition-all`}
          >
            <View className="p-1">
              <Ionicons 
                name={item.icon} 
                size={22} 
                color={item.active ? '#F4C753' : '#9CA3AF'} 
              />
            </View>
            <Text 
              className={`text-xs font-semibold ${
                item.active ? 'text-primary' : 'text-gray-500 dark:text-gray-400'
              }`}
            >
              {item.label}
            </Text>
          </Pressable>
        ))}
      </View>
    </View>
  );
}
