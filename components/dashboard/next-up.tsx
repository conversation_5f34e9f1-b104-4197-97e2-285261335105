import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { DashboardCard } from '@/components/ui/dashboard-card';

export function NextUp() {
  return (
    <DashboardCard>
      <Text className="text-xl font-bold mb-6 text-gray-900 dark:text-white">
        Next Up
      </Text>
      
      <View className="flex-row items-center space-x-4 p-4 bg-gray-50/80 dark:bg-gray-700/30 rounded-xl">
        <View className="bg-blue-100 dark:bg-blue-900/50 p-3 rounded-xl">
          <Ionicons 
            name="flask-outline" 
            size={24} 
            color="#3B82F6" 
          />
        </View>
        
        <View className="flex-1">
          <Text className="font-semibold text-gray-900 dark:text-white">
            Chemistry Lab Report
          </Text>
          <Text className="text-sm text-gray-500 dark:text-gray-400">
            Due in 2 days
          </Text>
        </View>
        
        <Pressable className="p-3 rounded-full bg-primary/20 text-primary hover:bg-primary/30 transition-colors">
          <Ionicons 
            name="arrow-forward" 
            size={24} 
            color="#F4C753" 
          />
        </Pressable>
      </View>
    </DashboardCard>
  );
}
